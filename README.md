# Reverie Agents - AI陪伴对话系统

Reverie Agents 是一个基于Python的高级AI对话陪伴系统，用户可以通过精美的现代化UI与名为Reverie的AI角色进行深度对话。

## 主要特性

- 🎭 **多人设模式**: 支持推理专家、妻子模式、旅行向导、职场导师等多种角色
- 🧠 **超长记忆**: 类似Augment的Context Engine，支持长对话上下文记忆
- 🌐 **智能联网**: 基于DuckDuckGo的自动搜索和结果整合
- 🎨 **2D Engine**: 支持SD、SDXL、Flux、illustrious等文生图模型
- 💻 **双UI模式**: PyQt6桌面应用 + Gradio Web界面
- 🚀 **本地部署**: 支持本地LLM模型，默认使用Lucy-128k
- 🔄 **流式输出**: 逐token实时输出，提供流畅体验

## 快速开始

### 环境配置
```bash
# 运行环境配置脚本
setup.bat

# 启动桌面应用
run_app.bat

# 启动Web界面
run_web.bat
```

### 默认模型
- **文本生成**: Lucy-128k (https://huggingface.co/Menlo/Lucy-128k-gguf)
- **图像生成**: 支持多种GGUF格式的Stable Diffusion模型

## 项目结构

```
Reverie Agents RIO/
├── core/                   # 核心功能模块
├── ui/                     # 用户界面
├── models/                 # 模型存储
├── personas/               # 人设配置
├── memory/                 # 记忆系统
├── search/                 # 联网搜索
├── engine_2d/              # 文生图引擎
├── docs/                   # 文档
├── logs/                   # 日志文件
└── config/                 # 配置文件
```

## 许可证

MIT License
